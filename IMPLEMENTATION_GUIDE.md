# Escrcpy Linux 沙盒解决方案 - 实施指南

## 🎯 解决方案概述

这个完整的解决方案为你的 Escrcpy 应用提供了自动化的 Linux 沙盒管理，彻底解决了用户需要手动修改 `.desktop` 文件的问题。

## 📋 已实现的功能

### ✅ 核心功能
- **自动环境检测** - 智能识别需要禁用沙盒的环境
- **安全配置** - 仅在必要时禁用沙盒，保持最佳安全性
- **详细日志** - 提供清晰的检测和配置过程日志
- **修复建议** - 为用户提供具体的问题修复步骤

### ✅ 检测条件
- Root 用户运行检测
- Wayland 环境检测
- 容器环境检测（Docker、Kubernetes）
- 特殊打包环境检测（Snap、Flatpak、AppImage）
- chrome-sandbox 文件权限检查

### ✅ 用户界面
- Vue 组件显示沙盒状态
- 多语言支持（中英文）
- 一键复制修复命令
- 友好的错误提示

## 🚀 快速开始

### 1. 验证集成

检查主进程是否正确集成：

<augment_code_snippet path="electron/main.js" mode="EXCERPT">
````javascript
import sandboxManager from './helpers/sandbox.js'

log.initialize({ preload: true })

// 配置沙盒设置 - 必须在 app.whenReady() 之前调用
sandboxManager.configureSandbox()
````
</augment_code_snippet>

### 2. 测试功能

运行应用并检查日志输出：

```bash
# 开发环境测试
npm run dev

# 查看控制台日志，应该看到类似输出：
# [INFO] Chromium sandbox will remain enabled
# 或
# [WARN] Disabling Chromium sandbox: Running as root user
```

### 3. 构建和部署

```bash
# 构建应用
npm run build:linux

# 运行 Linux 配置脚本（可选）
chmod +x scripts/linux-setup.sh
./scripts/linux-setup.sh
```

## 🔧 配置选项

### 环境变量控制

你可以通过环境变量控制沙盒行为：

```bash
# 强制禁用沙盒（调试用）
ESCRCPY_FORCE_NO_SANDBOX=1 escrcpy

# 启用详细日志
ESCRCPY_SANDBOX_DEBUG=1 escrcpy
```

### 自定义检测逻辑

如果需要添加新的检测条件，修改 `electron/helpers/sandbox.js`：

```javascript
// 添加新的环境检测
isCustomEnvironment() {
  return process.env.CUSTOM_ENV === 'problematic'
}

// 在 detectEnvironment() 中调用
if (this.isCustomEnvironment()) {
  this.shouldDisableSandbox = true
  this.disableReason = this.disableReason || 'Custom environment detected'
  checks.push('✓ Custom environment detected')
}
```

## 📱 用户界面集成

### 在设置页面显示沙盒状态

```vue
<template>
  <div class="settings-page">
    <!-- 其他设置项 -->
    
    <!-- 沙盒状态显示 -->
    <SandboxStatus />
  </div>
</template>

<script setup>
import SandboxStatus from '@/components/SandboxStatus.vue'
</script>
```

### 获取沙盒信息

```javascript
// 在任何 Vue 组件中
const sandboxInfo = await window.sandbox.getStatus()

if (sandboxInfo.shouldDisableSandbox) {
  console.log('沙盒已禁用:', sandboxInfo.disableReason)
  
  // 获取修复建议
  const suggestions = await window.sandbox.getFixSuggestions()
  suggestions.forEach(s => console.log(s.solution))
}
```

## 🧪 测试验证

### 运行单元测试

```bash
npm test test/sandbox.test.js
```

### 手动测试场景

1. **正常环境测试**
   ```bash
   # 以普通用户运行，应该保持沙盒启用
   escrcpy
   ```

2. **Root 用户测试**
   ```bash
   # 以 root 运行，应该自动禁用沙盒
   sudo escrcpy
   ```

3. **Wayland 环境测试**
   ```bash
   # 在 Wayland 会话中运行
   XDG_SESSION_TYPE=wayland escrcpy
   ```

4. **权限问题模拟**
   ```bash
   # 故意设置错误的 chrome-sandbox 权限
   sudo chmod 755 /opt/Escrcpy/chrome-sandbox
   escrcpy  # 应该检测到权限问题并提供修复建议
   ```

## 📦 打包配置

### electron-builder 配置

确保 `electron-builder.json` 包含正确的 Linux 配置：

```json
{
  "linux": {
    "target": [
      { "target": "AppImage", "arch": ["x64", "arm64"] },
      { "target": "deb", "arch": ["x64", "arm64"] }
    ],
    "extraResources": {
      "from": "electron/resources/extra",
      "to": "extra",
      "filter": ["common", "linux", "linux-${arch}"]
    }
  }
}
```

### 安装后脚本

为 DEB 包创建 postinst 脚本：

```bash
#!/bin/bash
# postinst script for escrcpy

CHROME_SANDBOX="/opt/Escrcpy/chrome-sandbox"

if [ -f "$CHROME_SANDBOX" ]; then
    # 设置正确的权限
    chown root:root "$CHROME_SANDBOX"
    chmod 4755 "$CHROME_SANDBOX"
    echo "chrome-sandbox permissions configured"
fi
```

## 🔍 故障排除

### 常见问题

1. **应用仍然报沙盒错误**
   - 检查日志输出确认检测逻辑是否正常工作
   - 验证 `sandboxManager.configureSandbox()` 是否在正确位置调用

2. **沙盒被意外禁用**
   - 检查环境变量设置
   - 查看日志中的检测结果

3. **修复建议不显示**
   - 确认 Vue 组件正确导入
   - 检查国际化文本是否正确加载

### 调试模式

启用详细日志进行调试：

```javascript
// 在 electron/helpers/sandbox.js 中临时添加
console.log('Sandbox detection results:', {
  platform: process.platform,
  isRoot: this.isRunningAsRoot(),
  isWayland: this.isWaylandSession(),
  isContainer: this.isRunningInContainer(),
  chromeSandboxPath: this.getChromeSandboxPath()
})
```

## 📈 性能考虑

- 沙盒检测只在应用启动时执行一次
- 检测逻辑轻量级，不会影响启动性能
- 文件系统检查使用同步 API，但仅在必要时执行

## 🔒 安全最佳实践

1. **最小权限原则** - 仅在必要时禁用沙盒
2. **透明度** - 详细记录所有配置决策
3. **用户教育** - 提供修复建议而不是永久禁用
4. **定期审查** - 定期检查新的环境条件

## 🌍 国际化扩展

添加新语言支持：

1. 在 `src/locales/languages/` 中添加新的语言文件
2. 添加沙盒相关的翻译键：
   ```json
   {
     "sandbox.enabled": "Chromium Sandbox Enabled",
     "sandbox.disabled": "Chromium Sandbox Disabled",
     "sandbox.fixSuggestions": "Fix Suggestions"
   }
   ```

## 📋 部署检查清单

- [ ] 主进程集成沙盒管理器
- [ ] IPC 处理程序正确注册
- [ ] 预加载脚本暴露 API
- [ ] Vue 组件可选集成
- [ ] 国际化文本添加
- [ ] 单元测试通过
- [ ] 手动测试验证
- [ ] 打包配置更新
- [ ] 文档更新完成

## 🎉 总结

这个解决方案提供了：

- ✅ **零用户配置** - 完全自动化处理
- ✅ **智能检测** - 精确识别需要禁用沙盒的环境
- ✅ **安全优先** - 保持最佳安全实践
- ✅ **用户友好** - 清晰的状态显示和修复建议
- ✅ **开发者友好** - 完整的测试和文档

用户现在可以直接运行 `escrcpy` 命令，无需任何手动配置，应用会智能处理所有沙盒相关的问题！
