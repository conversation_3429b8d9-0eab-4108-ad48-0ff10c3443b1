/**
 * 沙盒管理功能测试
 * 
 * 这个测试文件用于验证沙盒检测和配置功能
 * 注意：某些测试需要在特定环境下运行才能验证
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// 模拟 Electron 模块
vi.mock('electron', () => ({
  app: {
    commandLine: {
      appendSwitch: vi.fn()
    },
    getAppPath: vi.fn(() => '/opt/Escrcpy'),
    getPath: vi.fn(() => '/home/<USER>/Desktop')
  }
}))

// 模拟 fs 模块
vi.mock('node:fs', () => ({
  default: {
    existsSync: vi.fn(),
    statSync: vi.fn(),
    readFileSync: vi.fn()
  }
}))

// 模拟日志模块
vi.mock('$electron/helpers/log.js', () => ({
  default: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }
}))

// 模拟 process 模块
vi.mock('$electron/helpers/process.js', () => ({
  isPackaged: false
}))

describe('沙盒管理器测试', () => {
  let SandboxManager
  let sandboxManager
  let mockFs
  let mockApp
  let mockLog

  beforeEach(async () => {
    // 重置所有模拟
    vi.clearAllMocks()
    
    // 获取模拟的模块
    mockFs = await import('node:fs')
    mockApp = (await import('electron')).app
    mockLog = (await import('$electron/helpers/log.js')).default
    
    // 动态导入沙盒管理器（避免模块缓存问题）
    const sandboxModule = await import('../electron/helpers/sandbox.js')
    SandboxManager = sandboxModule.default.constructor
    sandboxManager = new SandboxManager()
  })

  describe('环境检测', () => {
    it('应该检测到 root 用户', () => {
      // 模拟 process.getuid 返回 0 (root)
      const originalGetuid = process.getuid
      process.getuid = vi.fn(() => 0)
      
      const isRoot = sandboxManager.isRunningAsRoot()
      expect(isRoot).toBe(true)
      
      // 恢复原始函数
      process.getuid = originalGetuid
    })

    it('应该检测到 Wayland 环境', () => {
      // 保存原始环境变量
      const originalEnv = process.env.XDG_SESSION_TYPE
      
      // 设置 Wayland 环境
      process.env.XDG_SESSION_TYPE = 'wayland'
      
      const isWayland = sandboxManager.isWaylandSession()
      expect(isWayland).toBe(true)
      
      // 恢复环境变量
      process.env.XDG_SESSION_TYPE = originalEnv
    })

    it('应该检测到容器环境', () => {
      // 模拟 Docker 环境
      mockFs.default.existsSync.mockImplementation((path) => {
        return path === '/.dockerenv'
      })
      
      const isContainer = sandboxManager.isRunningInContainer()
      expect(isContainer).toBe(true)
    })

    it('应该检测到问题环境', () => {
      // 保存原始环境变量
      const originalSnap = process.env.SNAP
      
      // 设置 Snap 环境
      process.env.SNAP = '/snap/escrcpy/current'
      
      const result = sandboxManager.isProblematicEnvironment()
      expect(result.problematic).toBe(true)
      expect(result.environment).toBe('SNAP')
      
      // 恢复环境变量
      process.env.SNAP = originalSnap
    })
  })

  describe('chrome-sandbox 权限检查', () => {
    it('应该检测到缺失的 chrome-sandbox 文件', () => {
      mockFs.default.existsSync.mockReturnValue(false)
      
      const result = sandboxManager.checkChromeSandboxPermissions()
      expect(result.valid).toBe(false)
      expect(result.reason).toContain('not found')
    })

    it('应该检测到权限不正确的 chrome-sandbox 文件', () => {
      mockFs.default.existsSync.mockReturnValue(true)
      mockFs.default.statSync.mockReturnValue({
        isFile: () => true,
        mode: parseInt('755', 8), // 缺少 setuid 位
        uid: 0
      })
      
      const result = sandboxManager.checkChromeSandboxPermissions()
      expect(result.valid).toBe(false)
      expect(result.reason).toContain('incorrect permissions')
    })

    it('应该检测到正确配置的 chrome-sandbox 文件', () => {
      mockFs.default.existsSync.mockReturnValue(true)
      mockFs.default.statSync.mockReturnValue({
        isFile: () => true,
        mode: parseInt('4755', 8), // 正确的权限
        uid: 0
      })
      
      const result = sandboxManager.checkChromeSandboxPermissions()
      expect(result.valid).toBe(true)
    })
  })

  describe('沙盒配置', () => {
    it('应该在 root 环境下禁用沙盒', () => {
      // 模拟 Linux 平台和 root 用户
      Object.defineProperty(process, 'platform', { value: 'linux' })
      process.getuid = vi.fn(() => 0)
      
      sandboxManager.configureSandbox()
      
      expect(sandboxManager.shouldDisableSandbox).toBe(true)
      expect(sandboxManager.disableReason).toContain('root')
      expect(mockApp.commandLine.appendSwitch).toHaveBeenCalledWith('no-sandbox')
    })

    it('应该在非 Linux 平台保持沙盒启用', () => {
      // 模拟 Windows 平台
      Object.defineProperty(process, 'platform', { value: 'win32' })
      
      sandboxManager.configureSandbox()
      
      expect(sandboxManager.shouldDisableSandbox).toBe(false)
      expect(mockApp.commandLine.appendSwitch).not.toHaveBeenCalled()
    })
  })

  describe('修复建议', () => {
    it('应该为权限问题提供修复建议', () => {
      // 设置 Linux 平台和权限问题
      Object.defineProperty(process, 'platform', { value: 'linux' })
      sandboxManager.shouldDisableSandbox = true
      sandboxManager.disableReason = 'chrome-sandbox has incorrect permissions'
      sandboxManager.chromeSandboxPath = '/opt/Escrcpy/chrome-sandbox'
      
      const suggestions = sandboxManager.getFixSuggestions()
      
      expect(suggestions).toHaveLength(1)
      expect(suggestions[0].issue).toContain('permissions')
      expect(suggestions[0].solution).toContain('sudo chown root:root')
      expect(suggestions[0].solution).toContain('sudo chmod 4755')
    })

    it('应该为 root 用户提供修复建议', () => {
      Object.defineProperty(process, 'platform', { value: 'linux' })
      process.getuid = vi.fn(() => 0)
      sandboxManager.shouldDisableSandbox = true
      
      const suggestions = sandboxManager.getFixSuggestions()
      
      const rootSuggestion = suggestions.find(s => s.issue.includes('root'))
      expect(rootSuggestion).toBeDefined()
      expect(rootSuggestion.solution).toContain('regular user')
    })
  })

  describe('状态信息', () => {
    it('应该返回完整的沙盒状态', () => {
      Object.defineProperty(process, 'platform', { value: 'linux' })
      sandboxManager.shouldDisableSandbox = true
      sandboxManager.disableReason = 'Test reason'
      
      const status = sandboxManager.getSandboxStatus()
      
      expect(status).toHaveProperty('platform', 'linux')
      expect(status).toHaveProperty('shouldDisableSandbox', true)
      expect(status).toHaveProperty('disableReason', 'Test reason')
      expect(status).toHaveProperty('isRoot')
      expect(status).toHaveProperty('isWayland')
      expect(status).toHaveProperty('isContainer')
      expect(status).toHaveProperty('environment')
    })
  })
})

describe('集成测试', () => {
  it('应该在典型的有问题的 Linux 环境中正确配置', async () => {
    // 模拟一个典型的有问题的环境
    Object.defineProperty(process, 'platform', { value: 'linux' })
    process.env.XDG_SESSION_TYPE = 'wayland'
    
    const mockFs = await import('node:fs')
    const mockApp = (await import('electron')).app
    
    // chrome-sandbox 文件存在但权限不正确
    mockFs.default.existsSync.mockReturnValue(true)
    mockFs.default.statSync.mockReturnValue({
      isFile: () => true,
      mode: parseInt('755', 8), // 权限不正确
      uid: 0
    })
    
    const sandboxModule = await import('../electron/helpers/sandbox.js')
    const manager = new sandboxModule.default.constructor()
    
    manager.configureSandbox()
    
    // 应该禁用沙盒并记录原因
    expect(manager.shouldDisableSandbox).toBe(true)
    expect(mockApp.commandLine.appendSwitch).toHaveBeenCalledWith('no-sandbox')
    
    // 应该提供修复建议
    const suggestions = manager.getFixSuggestions()
    expect(suggestions.length).toBeGreaterThan(0)
  })
})
