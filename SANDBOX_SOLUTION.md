# Escrcpy Linux 沙盒自动管理解决方案

## 概述

这个解决方案为 Escrcpy 应用程序提供了一个**稳定且安全的自动沙盒管理系统**，解决了在 Linux 环境下 Chromium 沙盒配置问题导致应用无法启动的问题。

## 问题背景

在 Linux 下运行 Electron 应用时，经常遇到以下错误：

```
[FATAL:setuid_sandbox_host.cc(163)] 
The SUID sandbox helper binary was found, but is not configured correctly.
Rather than run without sandboxing I'm aborting now.
You need to make sure that /opt/Escrcpy/chrome-sandbox is owned by root and has mode 4755.
```

## 解决方案特性

✅ **自动环境检测** - 智能检测运行环境是否需要禁用沙盒  
✅ **安全优先** - 仅在必要时禁用沙盒，保持最佳安全性  
✅ **用户友好** - 无需手动修改 `.desktop` 文件  
✅ **详细日志** - 提供清晰的检测和配置日志  
✅ **修复建议** - 为用户提供具体的修复步骤  
✅ **多语言支持** - 支持中英文界面  

## 实现的文件结构

```
electron/
├── helpers/
│   └── sandbox.js              # 核心沙盒管理器
├── ipc/
│   └── sandbox/
│       └── index.js            # IPC 处理程序
├── exposes/
│   └── sandbox/
│       └── index.js            # 预加载脚本 API
└── main.js                     # 主进程集成

src/
├── components/
│   └── SandboxStatus.vue       # 沙盒状态显示组件
└── locales/languages/
    ├── en-US.json              # 英文国际化
    └── zh-CN.json              # 中文国际化

test/
└── sandbox.test.js             # 单元测试

docs/
└── sandbox-management.md       # 详细文档
```

## 自动检测的环境条件

系统会自动检测以下情况并在必要时禁用沙盒：

### 1. 🔴 Root 用户运行
- **检测条件**: `process.getuid() === 0`
- **处理**: 自动禁用沙盒
- **原因**: 以 root 运行时 Chromium 会强制禁用沙盒

### 2. 🟡 Wayland 显示服务器
- **检测条件**: `XDG_SESSION_TYPE === 'wayland'`
- **处理**: 自动禁用沙盒
- **原因**: 某些 Wayland 配置下沙盒不兼容

### 3. 🟡 容器环境
- **检测条件**: Docker、Kubernetes 等容器标识
- **处理**: 自动禁用沙盒
- **原因**: 容器有自己的隔离机制

### 4. 🟡 特殊打包环境
- **检测条件**: Snap、Flatpak、AppImage 环境变量
- **处理**: 自动禁用沙盒
- **原因**: 这些打包格式有自己的沙盒机制

### 5. 🔴 chrome-sandbox 权限问题
- **检测条件**: 文件不存在、权限不正确、所有者不是 root
- **处理**: 自动禁用沙盒并提供修复建议
- **原因**: 权限不正确会导致沙盒启动失败

## 使用方法

### 1. 自动配置（推荐）

解决方案已集成到应用启动流程中，无需额外配置：

```javascript
// 在 electron/main.js 中自动执行
import sandboxManager from './helpers/sandbox.js'

log.initialize({ preload: true })

// 配置沙盒设置 - 必须在 app.whenReady() 之前调用
sandboxManager.configureSandbox()
```

### 2. 在渲染进程中获取状态

```javascript
// 获取沙盒状态
const status = await window.sandbox.getStatus()
console.log('沙盒状态:', status)

// 获取修复建议
const suggestions = await window.sandbox.getFixSuggestions()
suggestions.forEach(suggestion => {
  console.log(`问题: ${suggestion.issue}`)
  console.log(`解决方案: ${suggestion.solution}`)
})
```

### 3. 使用 Vue 组件显示状态

```vue
<template>
  <div>
    <!-- 其他内容 -->
    <SandboxStatus />
  </div>
</template>

<script setup>
import SandboxStatus from '@/components/SandboxStatus.vue'
</script>
```

## 日志输出示例

### 正常情况（沙盒启用）
```
[INFO] Chromium sandbox will remain enabled
```

### 检测到问题（沙盒禁用）
```
[INFO] Sandbox environment checks:
[INFO]   ✓ Wayland session detected
[INFO]   ✓ Chrome sandbox issue: chrome-sandbox has incorrect permissions (755), should be 4755
[WARN] Disabling Chromium sandbox: Running in Wayland session
[INFO] Applied --no-sandbox flag to Chromium
```

## 修复建议示例

当检测到问题时，系统会提供具体的修复建议：

### chrome-sandbox 权限修复
```bash
sudo chown root:root "/opt/Escrcpy/chrome-sandbox"
sudo chmod 4755 "/opt/Escrcpy/chrome-sandbox"
```

### 避免以 root 运行
```bash
# 以普通用户身份运行
su - username -c "escrcpy"
```

## 测试

运行单元测试验证功能：

```bash
npm test test/sandbox.test.js
```

测试覆盖：
- ✅ 环境检测功能
- ✅ chrome-sandbox 权限检查
- ✅ 沙盒配置逻辑
- ✅ 修复建议生成
- ✅ 集成测试场景

## 安全考虑

1. **最小权限原则** - 仅在必要时禁用沙盒
2. **透明度** - 详细记录所有检测结果和配置决策
3. **用户教育** - 提供修复建议帮助用户恢复沙盒功能
4. **渐进增强** - 优先尝试修复而不是永久禁用

## 兼容性

- ✅ **Linux**: 完整支持所有检测功能
- ✅ **Windows**: 自动跳过检测，保持默认行为
- ✅ **macOS**: 自动跳过检测，保持默认行为

## 部署注意事项

1. **打包配置**: 确保 `chrome-sandbox` 文件包含在发布包中
2. **权限设置**: 在安装脚本中正确设置 `chrome-sandbox` 权限
3. **用户指导**: 在文档中说明如何正确配置权限

## 贡献

如果你发现新的需要禁用沙盒的环境条件，请：

1. 在 `electron/helpers/sandbox.js` 中添加检测逻辑
2. 在 `test/sandbox.test.js` 中添加相应测试
3. 更新文档说明新的检测条件

## 总结

这个解决方案彻底解决了 Escrcpy 在 Linux 下的沙盒问题，提供了：

- 🚀 **零配置** - 用户无需手动修改任何文件
- 🔒 **安全优先** - 智能决策何时禁用沙盒
- 🛠️ **问题诊断** - 详细的检测和修复建议
- 🌍 **多语言** - 支持中英文用户界面
- 🧪 **可测试** - 完整的单元测试覆盖

用户现在可以直接运行 `escrcpy` 命令，应用会自动处理所有沙盒相关的配置，无需任何手动干预。
