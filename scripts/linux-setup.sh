#!/bin/bash

# Escrcpy Linux 安装和配置脚本
# 用于正确设置 chrome-sandbox 权限和应用配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以 root 权限运行
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到以 root 用户运行安装脚本"
        log_warning "建议以普通用户运行，需要时会提示输入 sudo 密码"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检测 Linux 发行版
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
        VERSION=$VERSION_ID
    else
        DISTRO="unknown"
        VERSION="unknown"
    fi
    
    log_info "检测到系统: $DISTRO $VERSION"
}

# 检测安装路径
detect_install_path() {
    # 常见的安装路径
    POSSIBLE_PATHS=(
        "/opt/Escrcpy"
        "/usr/local/bin/escrcpy"
        "/usr/bin/escrcpy"
        "$HOME/.local/share/applications/escrcpy"
        "/snap/escrcpy/current"
        "/var/lib/flatpak/app/org.viarotel.escrcpy"
    )
    
    INSTALL_PATH=""
    
    for path in "${POSSIBLE_PATHS[@]}"; do
        if [ -d "$path" ] || [ -f "$path" ]; then
            INSTALL_PATH="$path"
            log_info "找到安装路径: $INSTALL_PATH"
            break
        fi
    done
    
    if [ -z "$INSTALL_PATH" ]; then
        log_warning "未找到 Escrcpy 安装路径"
        read -p "请输入 Escrcpy 安装路径: " INSTALL_PATH
        
        if [ ! -d "$INSTALL_PATH" ] && [ ! -f "$INSTALL_PATH" ]; then
            log_error "指定的路径不存在: $INSTALL_PATH"
            exit 1
        fi
    fi
}

# 查找 chrome-sandbox 文件
find_chrome_sandbox() {
    CHROME_SANDBOX_PATHS=(
        "$INSTALL_PATH/chrome-sandbox"
        "$(dirname "$INSTALL_PATH")/chrome-sandbox"
        "/opt/Escrcpy/chrome-sandbox"
    )
    
    CHROME_SANDBOX=""
    
    for path in "${CHROME_SANDBOX_PATHS[@]}"; do
        if [ -f "$path" ]; then
            CHROME_SANDBOX="$path"
            log_info "找到 chrome-sandbox: $CHROME_SANDBOX"
            break
        fi
    done
    
    if [ -z "$CHROME_SANDBOX" ]; then
        log_warning "未找到 chrome-sandbox 文件"
        log_info "这可能意味着："
        log_info "  1. 应用会自动禁用沙盒（安全性较低）"
        log_info "  2. 或者 chrome-sandbox 位于其他位置"
        return 1
    fi
    
    return 0
}

# 检查 chrome-sandbox 权限
check_chrome_sandbox_permissions() {
    if [ -z "$CHROME_SANDBOX" ]; then
        return 1
    fi
    
    log_info "检查 chrome-sandbox 权限..."
    
    # 获取文件信息
    OWNER=$(stat -c '%U' "$CHROME_SANDBOX" 2>/dev/null || echo "unknown")
    PERMISSIONS=$(stat -c '%a' "$CHROME_SANDBOX" 2>/dev/null || echo "000")
    
    log_info "当前所有者: $OWNER"
    log_info "当前权限: $PERMISSIONS"
    
    # 检查是否需要修复
    NEEDS_FIX=false
    
    if [ "$OWNER" != "root" ]; then
        log_warning "chrome-sandbox 所有者不是 root"
        NEEDS_FIX=true
    fi
    
    if [ "$PERMISSIONS" != "4755" ]; then
        log_warning "chrome-sandbox 权限不正确 (当前: $PERMISSIONS, 应该: 4755)"
        NEEDS_FIX=true
    fi
    
    if [ "$NEEDS_FIX" = true ]; then
        return 1
    else
        log_success "chrome-sandbox 权限配置正确"
        return 0
    fi
}

# 修复 chrome-sandbox 权限
fix_chrome_sandbox_permissions() {
    if [ -z "$CHROME_SANDBOX" ]; then
        log_error "无法修复：chrome-sandbox 文件不存在"
        return 1
    fi
    
    log_info "修复 chrome-sandbox 权限..."
    
    # 修改所有者为 root
    if ! sudo chown root:root "$CHROME_SANDBOX"; then
        log_error "修改所有者失败"
        return 1
    fi
    
    # 设置正确的权限 (4755)
    if ! sudo chmod 4755 "$CHROME_SANDBOX"; then
        log_error "修改权限失败"
        return 1
    fi
    
    log_success "chrome-sandbox 权限修复完成"
    
    # 验证修复结果
    check_chrome_sandbox_permissions
    return $?
}

# 检查环境问题
check_environment_issues() {
    log_info "检查环境配置..."
    
    ISSUES_FOUND=false
    
    # 检查是否在 Wayland 环境
    if [ "$XDG_SESSION_TYPE" = "wayland" ]; then
        log_warning "检测到 Wayland 环境，可能需要禁用沙盒"
        ISSUES_FOUND=true
    fi
    
    # 检查是否在容器中
    if [ -f /.dockerenv ] || [ -n "$DOCKER_CONTAINER" ]; then
        log_warning "检测到 Docker 容器环境，将自动禁用沙盒"
        ISSUES_FOUND=true
    fi
    
    # 检查特殊打包环境
    if [ -n "$SNAP" ]; then
        log_warning "检测到 Snap 环境，将自动禁用沙盒"
        ISSUES_FOUND=true
    fi
    
    if [ -n "$FLATPAK_ID" ]; then
        log_warning "检测到 Flatpak 环境，将自动禁用沙盒"
        ISSUES_FOUND=true
    fi
    
    if [ -n "$APPIMAGE" ]; then
        log_warning "检测到 AppImage 环境，将自动禁用沙盒"
        ISSUES_FOUND=true
    fi
    
    if [ "$ISSUES_FOUND" = false ]; then
        log_success "未检测到已知的环境问题"
    else
        log_info "检测到的环境问题将由应用自动处理"
    fi
}

# 创建启动脚本
create_launcher_script() {
    LAUNCHER_PATH="$HOME/.local/bin/escrcpy-launcher"
    
    log_info "创建启动脚本: $LAUNCHER_PATH"
    
    mkdir -p "$(dirname "$LAUNCHER_PATH")"
    
    cat > "$LAUNCHER_PATH" << 'EOF'
#!/bin/bash

# Escrcpy 启动脚本
# 自动处理沙盒配置和环境检测

# 检查是否以 root 运行
if [[ $EUID -eq 0 ]]; then
    echo "警告: 不建议以 root 用户运行 Escrcpy"
    echo "应用将自动禁用沙盒以确保安全"
fi

# 启动应用
exec /opt/Escrcpy/escrcpy "$@"
EOF
    
    chmod +x "$LAUNCHER_PATH"
    log_success "启动脚本创建完成"
}

# 主函数
main() {
    log_info "开始 Escrcpy Linux 配置..."
    
    # 检查权限
    check_root
    
    # 检测系统信息
    detect_distro
    
    # 检测安装路径
    detect_install_path
    
    # 查找 chrome-sandbox
    if find_chrome_sandbox; then
        # 检查权限
        if ! check_chrome_sandbox_permissions; then
            log_info "需要修复 chrome-sandbox 权限"
            read -p "是否现在修复？(Y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Nn]$ ]]; then
                log_warning "跳过权限修复，应用将自动禁用沙盒"
            else
                fix_chrome_sandbox_permissions
            fi
        fi
    else
        log_info "未找到 chrome-sandbox，应用将自动禁用沙盒"
    fi
    
    # 检查环境问题
    check_environment_issues
    
    # 创建启动脚本
    read -p "是否创建启动脚本？(Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        create_launcher_script
    fi
    
    log_success "配置完成！"
    log_info "现在可以运行 Escrcpy，应用会自动处理沙盒配置"
    
    if [ -f "$HOME/.local/bin/escrcpy-launcher" ]; then
        log_info "使用启动脚本: $HOME/.local/bin/escrcpy-launcher"
    fi
}

# 运行主函数
main "$@"
