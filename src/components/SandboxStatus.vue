<template>
  <div v-if="showStatus" class="sandbox-status">
    <el-alert
      :title="alertTitle"
      :type="alertType"
      :closable="false"
      show-icon
    >
      <template #default>
        <div class="sandbox-details">
          <p>{{ status.disableReason }}</p>
          
          <div v-if="suggestions.length > 0" class="fix-suggestions">
            <h4>{{ $t('sandbox.fixSuggestions') }}</h4>
            <div
              v-for="(suggestion, index) in suggestions"
              :key="index"
              class="suggestion-item"
            >
              <h5>{{ suggestion.issue }}</h5>
              <p>{{ suggestion.description }}</p>
              <el-input
                v-model="suggestion.solution"
                readonly
                class="solution-command"
              >
                <template #append>
                  <el-button @click="copySolution(suggestion.solution)">
                    {{ $t('common.copy') }}
                  </el-button>
                </template>
              </el-input>
            </div>
          </div>
        </div>
      </template>
    </el-alert>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const status = ref({})
const suggestions = ref([])
const loading = ref(true)

const showStatus = computed(() => {
  return status.value.platform === 'linux' && status.value.shouldDisableSandbox
})

const alertType = computed(() => {
  if (status.value.isRoot) return 'error'
  if (status.value.disableReason?.includes('permissions')) return 'warning'
  return 'info'
})

const alertTitle = computed(() => {
  if (status.value.shouldDisableSandbox) {
    return window.t('sandbox.disabled')
  }
  return window.t('sandbox.enabled')
})

const copySolution = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success(window.t('common.copySuccess'))
  } catch (error) {
    console.error('Failed to copy:', error)
    ElMessage.error(window.t('common.copyFailed'))
  }
}

const loadSandboxStatus = async () => {
  try {
    loading.value = true
    status.value = await window.sandbox.getStatus()
    
    if (status.value.shouldDisableSandbox) {
      suggestions.value = await window.sandbox.getFixSuggestions()
    }
  } catch (error) {
    console.error('Failed to load sandbox status:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadSandboxStatus()
})
</script>

<style scoped>
.sandbox-status {
  margin: 16px 0;
}

.sandbox-details {
  margin-top: 8px;
}

.fix-suggestions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-light);
}

.fix-suggestions h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.suggestion-item {
  margin-bottom: 16px;
}

.suggestion-item h5 {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.suggestion-item p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.solution-command {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.solution-command :deep(.el-input__inner) {
  font-family: inherit;
  font-size: inherit;
}
</style>
