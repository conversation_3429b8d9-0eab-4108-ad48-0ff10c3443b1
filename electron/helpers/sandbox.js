import { app } from 'electron'
import fs from 'node:fs'
import path from 'node:path'
import { isPackaged } from './process.js'
import log from './log.js'

/**
 * 沙盒配置管理器
 * 自动检测 Linux 环境并决定是否需要禁用 Chromium 沙盒
 */
class SandboxManager {
  constructor() {
    this.shouldDisableSandbox = false
    this.disableReason = null
    this.chromeSandboxPath = null
  }

  /**
   * 检测是否以 root 用户运行
   */
  isRunningAsRoot() {
    try {
      return process.getuid && process.getuid() === 0
    } catch (error) {
      return false
    }
  }

  /**
   * 检测是否在 Wayland 环境下运行
   */
  isWaylandSession() {
    return (
      process.env.XDG_SESSION_TYPE === 'wayland' ||
      process.env.WAYLAND_DISPLAY ||
      process.env.GDK_BACKEND === 'wayland'
    )
  }

  /**
   * 检测是否在容器环境中运行
   */
  isRunningInContainer() {
    try {
      // 检查常见的容器环境标识
      return (
        fs.existsSync('/.dockerenv') ||
        process.env.container ||
        process.env.DOCKER_CONTAINER ||
        process.env.KUBERNETES_SERVICE_HOST ||
        (fs.existsSync('/proc/1/cgroup') && 
         fs.readFileSync('/proc/1/cgroup', 'utf8').includes('docker'))
      )
    } catch (error) {
      return false
    }
  }

  /**
   * 获取 chrome-sandbox 文件路径
   */
  getChromeSandboxPath() {
    if (this.chromeSandboxPath) {
      return this.chromeSandboxPath
    }

    // 可能的 chrome-sandbox 路径
    const possiblePaths = [
      path.join(process.resourcesPath, 'chrome-sandbox'),
      path.join(app.getAppPath(), 'chrome-sandbox'),
      path.join(process.execPath, '..', 'chrome-sandbox'),
      '/opt/Escrcpy/chrome-sandbox'
    ]

    for (const sandboxPath of possiblePaths) {
      if (fs.existsSync(sandboxPath)) {
        this.chromeSandboxPath = sandboxPath
        return sandboxPath
      }
    }

    return null
  }

  /**
   * 检查 chrome-sandbox 文件权限
   */
  checkChromeSandboxPermissions() {
    const sandboxPath = this.getChromeSandboxPath()
    
    if (!sandboxPath) {
      return { valid: false, reason: 'chrome-sandbox file not found' }
    }

    try {
      const stats = fs.statSync(sandboxPath)
      
      // 检查文件是否存在且可执行
      if (!stats.isFile()) {
        return { valid: false, reason: 'chrome-sandbox is not a file' }
      }

      // 检查文件权限 (应该是 4755 且 owner 是 root)
      const mode = stats.mode & parseInt('777', 8)
      const isSetuid = (stats.mode & parseInt('4000', 8)) !== 0
      
      if (!isSetuid || mode !== parseInt('755', 8)) {
        return { 
          valid: false, 
          reason: `chrome-sandbox has incorrect permissions (${mode.toString(8)}), should be 4755` 
        }
      }

      // 在非 root 环境下，检查 owner 是否为 root
      if (!this.isRunningAsRoot() && stats.uid !== 0) {
        return { 
          valid: false, 
          reason: 'chrome-sandbox is not owned by root' 
        }
      }

      return { valid: true }
    } catch (error) {
      return { 
        valid: false, 
        reason: `Failed to check chrome-sandbox permissions: ${error.message}` 
      }
    }
  }

  /**
   * 检测是否在已知有问题的环境中
   */
  isProblematicEnvironment() {
    // 检查一些已知有沙盒问题的环境
    const problematicEnvs = [
      'SNAP',           // Snap 包环境
      'FLATPAK_ID',     // Flatpak 环境
      'APPIMAGE',       // AppImage 环境
    ]

    for (const env of problematicEnvs) {
      if (process.env[env]) {
        return { problematic: true, environment: env }
      }
    }

    return { problematic: false }
  }

  /**
   * 执行完整的环境检测
   */
  detectEnvironment() {
    // 只在 Linux 平台执行检测
    if (process.platform !== 'linux') {
      return
    }

    const checks = []

    // 检查是否以 root 用户运行
    if (this.isRunningAsRoot()) {
      this.shouldDisableSandbox = true
      this.disableReason = 'Running as root user'
      checks.push('✓ Root user detected')
    }

    // 检查 Wayland 环境
    if (this.isWaylandSession()) {
      this.shouldDisableSandbox = true
      this.disableReason = this.disableReason || 'Running in Wayland session'
      checks.push('✓ Wayland session detected')
    }

    // 检查容器环境
    if (this.isRunningInContainer()) {
      this.shouldDisableSandbox = true
      this.disableReason = this.disableReason || 'Running in container environment'
      checks.push('✓ Container environment detected')
    }

    // 检查问题环境
    const envCheck = this.isProblematicEnvironment()
    if (envCheck.problematic) {
      this.shouldDisableSandbox = true
      this.disableReason = this.disableReason || `Running in ${envCheck.environment} environment`
      checks.push(`✓ ${envCheck.environment} environment detected`)
    }

    // 检查 chrome-sandbox 权限
    const sandboxCheck = this.checkChromeSandboxPermissions()
    if (!sandboxCheck.valid) {
      this.shouldDisableSandbox = true
      this.disableReason = this.disableReason || sandboxCheck.reason
      checks.push(`✓ Chrome sandbox issue: ${sandboxCheck.reason}`)
    }

    // 记录检测结果
    if (checks.length > 0) {
      log.info('Sandbox environment checks:')
      checks.forEach(check => log.info(`  ${check}`))
    }

    if (this.shouldDisableSandbox) {
      log.warn(`Disabling Chromium sandbox: ${this.disableReason}`)
    } else {
      log.info('Chromium sandbox will remain enabled')
    }
  }

  /**
   * 配置应用沙盒设置
   */
  configureSandbox() {
    this.detectEnvironment()

    if (this.shouldDisableSandbox) {
      // 添加 --no-sandbox 参数
      app.commandLine.appendSwitch('no-sandbox')
      
      // 可选：添加其他相关的安全参数
      app.commandLine.appendSwitch('disable-dev-shm-usage')
      
      log.info('Applied --no-sandbox flag to Chromium')
    }
  }

  /**
   * 获取沙盒状态信息
   */
  getSandboxStatus() {
    return {
      platform: process.platform,
      shouldDisableSandbox: this.shouldDisableSandbox,
      disableReason: this.disableReason,
      chromeSandboxPath: this.chromeSandboxPath,
      isRoot: this.isRunningAsRoot(),
      isWayland: this.isWaylandSession(),
      isContainer: this.isRunningInContainer(),
      environment: this.isProblematicEnvironment()
    }
  }

  /**
   * 提供修复建议
   */
  getFixSuggestions() {
    if (process.platform !== 'linux' || !this.shouldDisableSandbox) {
      return []
    }

    const suggestions = []
    const sandboxPath = this.getChromeSandboxPath()

    if (sandboxPath && this.disableReason?.includes('permissions')) {
      suggestions.push({
        issue: 'Chrome sandbox permissions',
        solution: `Run: sudo chown root:root "${sandboxPath}" && sudo chmod 4755 "${sandboxPath}"`,
        description: 'Fix chrome-sandbox file ownership and permissions'
      })
    }

    if (this.isRunningAsRoot()) {
      suggestions.push({
        issue: 'Running as root',
        solution: 'Run the application as a regular user instead of root',
        description: 'Running as root disables the sandbox for security reasons'
      })
    }

    return suggestions
  }
}

// 创建单例实例
const sandboxManager = new SandboxManager()

export default sandboxManager
