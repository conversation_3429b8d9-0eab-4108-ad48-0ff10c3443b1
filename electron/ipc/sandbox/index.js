import { ipcMain } from 'electron'
import sandboxManager from '$electron/helpers/sandbox.js'

/**
 * 沙盒状态相关的 IPC 处理程序
 */
export default () => {
  // 获取沙盒状态信息
  ipcMain.handle('get-sandbox-status', () => {
    return sandboxManager.getSandboxStatus()
  })

  // 获取修复建议
  ipcMain.handle('get-sandbox-fix-suggestions', () => {
    return sandboxManager.getFixSuggestions()
  })

  // 重新检测沙盒环境（用于调试）
  ipcMain.handle('detect-sandbox-environment', () => {
    sandboxManager.detectEnvironment()
    return sandboxManager.getSandboxStatus()
  })
}
