import { electronAPI } from '@electron-toolkit/preload'

/**
 * 沙盒状态相关的 API
 */
const sandbox = {
  /**
   * 获取当前沙盒状态
   */
  async getStatus() {
    return await electronAPI.ipcRenderer.invoke('get-sandbox-status')
  },

  /**
   * 获取修复建议
   */
  async getFixSuggestions() {
    return await electronAPI.ipcRenderer.invoke('get-sandbox-fix-suggestions')
  },

  /**
   * 重新检测沙盒环境
   */
  async detectEnvironment() {
    return await electronAPI.ipcRenderer.invoke('detect-sandbox-environment')
  }
}

export default sandbox
