# Linux 沙盒管理解决方案

本文档介绍了 Escrcpy 应用程序在 Linux 环境下的自动沙盒管理解决方案。

## 问题背景

Electron 应用在 Linux 下运行时，可能会遇到 Chromium 沙盒配置问题，导致应用无法启动：

```
The SUID sandbox helper binary was found, but is not configured correctly.
Rather than run without sandboxing I'm aborting now.
You need to make sure that /opt/Escrcpy/chrome-sandbox is owned by root and has mode 4755.
```

## 解决方案

我们实现了一个自动检测和配置系统，能够：

1. **自动检测环境**：检测当前运行环境是否需要禁用沙盒
2. **智能配置**：仅在必要时禁用沙盒，保持最佳安全性
3. **提供修复建议**：为用户提供具体的修复步骤

## 检测条件

系统会自动检测以下情况并在必要时禁用沙盒：

### 1. Root 用户运行
- **检测**：`process.getuid() === 0`
- **原因**：以 root 用户运行时，Chromium 会自动禁用沙盒以避免安全风险

### 2. Wayland 环境
- **检测**：`XDG_SESSION_TYPE === 'wayland'` 或相关环境变量
- **原因**：某些 Wayland 配置下沙盒可能不兼容

### 3. 容器环境
- **检测**：Docker、Kubernetes 等容器环境标识
- **原因**：容器环境通常有自己的隔离机制

### 4. 特殊打包环境
- **检测**：Snap、Flatpak、AppImage 等
- **原因**：这些打包格式有自己的沙盒机制

### 5. chrome-sandbox 权限问题
- **检测**：检查文件存在性、权限和所有者
- **原因**：不正确的权限会导致沙盒启动失败

## API 使用

### 在渲染进程中获取沙盒状态

```javascript
// 获取当前沙盒状态
const status = await window.sandbox.getStatus()
console.log('沙盒状态:', status)

// 获取修复建议
const suggestions = await window.sandbox.getFixSuggestions()
suggestions.forEach(suggestion => {
  console.log(`问题: ${suggestion.issue}`)
  console.log(`解决方案: ${suggestion.solution}`)
  console.log(`说明: ${suggestion.description}`)
})

// 重新检测环境（调试用）
const newStatus = await window.sandbox.detectEnvironment()
```

### 状态对象结构

```javascript
{
  platform: 'linux',                    // 操作系统平台
  shouldDisableSandbox: true,           // 是否应该禁用沙盒
  disableReason: 'Running as root user', // 禁用原因
  chromeSandboxPath: '/opt/Escrcpy/chrome-sandbox', // chrome-sandbox 路径
  isRoot: true,                         // 是否以 root 运行
  isWayland: false,                     // 是否 Wayland 环境
  isContainer: false,                   // 是否容器环境
  environment: {                        // 特殊环境检测
    problematic: false,
    environment: null
  }
}
```

## 修复建议

当检测到沙盒问题时，系统会提供具体的修复建议：

### chrome-sandbox 权限修复

```bash
# 修复 chrome-sandbox 文件权限
sudo chown root:root "/opt/Escrcpy/chrome-sandbox"
sudo chmod 4755 "/opt/Escrcpy/chrome-sandbox"
```

### 避免以 root 运行

```bash
# 以普通用户身份运行应用
su - username -c "escrcpy"
```

## 安全考虑

1. **最小权限原则**：仅在必要时禁用沙盒
2. **详细日志**：记录所有检测结果和配置决策
3. **用户通知**：在禁用沙盒时提供明确的原因说明
4. **修复指导**：提供具体的修复步骤以恢复沙盒功能

## 日志示例

```
[INFO] Sandbox environment checks:
[INFO]   ✓ Root user detected
[INFO]   ✓ Chrome sandbox issue: chrome-sandbox has incorrect permissions (755), should be 4755
[WARN] Disabling Chromium sandbox: Running as root user
[INFO] Applied --no-sandbox flag to Chromium
```

## 最佳实践

1. **定期检查**：定期检查 chrome-sandbox 文件权限
2. **避免 root**：尽量避免以 root 用户运行应用
3. **监控日志**：关注应用启动时的沙盒相关日志
4. **及时更新**：保持应用和系统更新以获得最佳兼容性

## 故障排除

### 应用仍然无法启动

1. 检查日志输出中的具体错误信息
2. 手动验证 chrome-sandbox 文件权限
3. 尝试在不同用户账户下运行
4. 检查系统的 SELinux/AppArmor 配置

### 沙盒被意外禁用

1. 检查环境变量设置
2. 验证用户权限
3. 检查是否在容器或特殊环境中运行

通过这个自动化解决方案，用户无需手动修改 `.desktop` 文件，应用会根据运行环境自动做出最佳配置决策。
